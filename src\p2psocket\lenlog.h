// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#ifndef LLOG_H
#define LLOG_H
#include <string>
#ifdef _WIN32
#include "common.h"
#else
#include <android/log.h>
#endif
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <mutex>
#include <sstream>

#include "common_defines.h"
#include "util.h"
#ifdef _WIN32
static const char* keyPath = "Software\\Lenovo\\DataTunnel";

static void inline WriteConfigValue(const char* valueName, DWORD valueData) {
  HKEY hKey;
  if (RegCreateKeyExA(HKEY_CURRENT_USER, keyPath, 0, nullptr,
                      REG_OPTION_NON_VOLATILE, KEY_WRITE, nullptr, &hKey,
                      nullptr) == ERROR_SUCCESS) {
    if (RegSetValueExA(hKey, valueName, 0, REG_DWORD,
                       reinterpret_cast<const BYTE*>(&valueData),
                       sizeof(DWORD)) == ERROR_SUCCESS) {
      std::cout << "Successfully wrote config value: " << valueName
                << std::endl;
    } else {
      std::cout << "Failed to write config value." << std::endl;
    }

    RegCloseKey(hKey);
  } else {
    std::cout << "Failed to create/open registry key." << std::endl;
  }
}

static int ReadConfigValue(const char* valueName, DWORD& valueData) {
  HKEY hKey;
  DWORD dataSize = sizeof(DWORD);
  int ret = 0;
  if (RegOpenKeyExA(HKEY_CURRENT_USER, keyPath, 0, KEY_READ, &hKey) ==
      ERROR_SUCCESS) {
    if (RegQueryValueExA(hKey, valueName, nullptr, nullptr,
                         reinterpret_cast<BYTE*>(&valueData),
                         &dataSize) == ERROR_SUCCESS) {
      std::cout << "Config value " << valueName << ": " << valueData
                << std::endl;
    } else {
      std::cout << "Failed to read config value." << std::endl;
      ret = -1;
    }
    RegCloseKey(hKey);
  } else {
    std::cout << "Failed to open registry key." << std::endl;
    ret = -1;
  }

  return ret;
}
#endif
#ifdef _WIN32
class RotatingLogFile {
 private:
  std::ofstream file_;
  std::string base_filename_;
  const std::streamsize max_size_ = 64 * 1024 * 1024;  // 64MB in bytes
  int current_file_number_ = 0;

  bool file_exists(const std::string& filename) {
    return GetFileAttributesA(filename.c_str()) != INVALID_FILE_ATTRIBUTES;
  }

  std::streamsize get_file_size(const std::string& filename) {
    WIN32_FILE_ATTRIBUTE_DATA fad;
    if (!GetFileAttributesExA(filename.c_str(), GetFileExInfoStandard, &fad)) {
      return 0;
    }
    LARGE_INTEGER size;
    size.HighPart = fad.nFileSizeHigh;
    size.LowPart = fad.nFileSizeLow;
    return size.QuadPart;
  }

  std::string get_next_filename() {
    std::ostringstream oss;
    oss << base_filename_ << "." << std::setw(3) << std::setfill('0')
        << current_file_number_;
    return oss.str();
  }

  void open_next_file() {
    if (file_.is_open()) {
      file_.close();
    }

    std::string next_filename = get_next_filename();
    while (file_exists(next_filename)) {
      current_file_number_++;
      next_filename = get_next_filename();
    }

    file_.open(next_filename, std::ios::out | std::ios::app);
    if (!file_.is_open()) {
      std::cerr << "Failed to open file: " << next_filename << std::endl;
    }
  }

  void check_and_rotate() {
    if (!file_.is_open())
      return;
    std::streamsize current_size = get_file_size(get_next_filename());
    if (current_size >= max_size_) {
      current_file_number_++;
      open_next_file();
    }
  }

 public:
  RotatingLogFile(const std::string& filename);
  RotatingLogFile();

  ~RotatingLogFile();
  bool is_open() const { return file_.is_open(); }
  void close() {
    if (file_.is_open()) {
      file_.close();
    }
  }
  void open(const char* filename) {
    close();  // Close the current file if it's open

    if (filename != nullptr) {
      base_filename_ = filename;
      current_file_number_ = 0;
    }

    open_next_file();
  }
  template <typename T>
  RotatingLogFile& operator<<(const T& data) {
    check_and_rotate();
    if (file_.is_open())
      file_ << data;
    return *this;
  }
  RotatingLogFile& operator<<(std::ostream& (*manip)(std::ostream&)) {
    check_and_rotate();
    if (file_.is_open())
      manip(file_);
    return *this;
  }
  void flush() {
    if (file_.is_open()) {
      file_.flush();
    }
  }
};
#endif
class LLog {
 public:
  static void Log(std::string msg) {
#ifdef _WIN32
    char realtime[50] = {0};
    SYSTEMTIME sys;
    GetLocalTime(&sys);
    // 09-14 17:25:34.825
    snprintf(realtime, sizeof(realtime), "[%02d-%02d %02d:%02d:%02d.%03d] ",
             sys.wMonth, sys.wDay, sys.wHour, sys.wMinute, sys.wSecond,
             sys.wMilliseconds);
    std::string out = "LLog " + std::string(realtime) + msg + "\n";
    std::cout << out;
    // OutputDebugStringA(out.c_str());
#else
    std::string out = "LLog " + msg + "\n";
    __android_log_print(ANDROID_LOG_INFO, "LLog", "%s", out.c_str());
    std::cout << out;
#endif
  }
  static void Init(LOG_LEVEL level, const char* path) {
    // std::cout << "LLog init level =" << level << " path = " << path <<
    // std::endl;
#ifdef _WIN32
    mutex_.lock();
    if (!isinit_) {
      isinit_ = true;
      if (!file_.is_open() && path) {
        file_.open(path);
      }
      mutex_.unlock();

      DWORD data;
      // WriteConfigValue("loglevel", 4);
      int ret = ReadConfigValue("loglevel", data);
      if (ret == 0) {
        std::cout << "Config value loglevel: " << data << std::endl;
        switch (data) {
          case 0:
            log_level_ = LOG_NONE;
            break;
          case 1:
            log_level_ = LOG_ERROR;
            break;
          case 2:
            log_level_ = LOG_WARN;
            break;
          case 3:
            log_level_ = LOG_INFO;
            break;
          case 4:
            log_level_ = LOG_DEBUG;
            break;
          case 5:
            log_level_ = LOG_VERBOSE;
            break;
          default:
            log_level_ = LOG_ERROR;
            break;
        }
      } else {
        if (level >= P2P_LOG_NONE && level <= P2P_LOG_VERBOSE)
          log_level_ = static_cast<log_level>(level);
      }
    } else {
      mutex_.unlock();
    }
#else
    const char* filepath = "/sdcard/nolog";
    if (access(filepath, F_OK) == 0) {
      log_level_ = LOG_ERROR;
    } else {
      if (level >= P2P_LOG_NONE && level <= P2P_LOG_VERBOSE) {
        log_level_ = static_cast<log_level>(level);
      } else {
        log_level_ = LOG_INFO;
      }
    }
    const char* filepath2 = "/sdcard/datatunnellog";
    if (access(filepath2, F_OK) == 0) {
      log_level_ = LOG_INFO;
    }
#endif
    return;
  }
  static void Uninit() {
#ifdef _WIN32
    mutex_.lock();
    if (isinit_) {
      if (file_.is_open())
        file_.close();
      isinit_ = false;
    }
    mutex_.unlock();
#endif
    return;
  }
  static void SetLogLevel(log_level level) {
#ifndef _WIN32
    mutex_.lock();
    log_level_ = level;
    if (!isinit_)
      isinit_ = true;
    mutex_.unlock();
    return;
#endif
  }

  static void Log(log_level level, const char* format, ...) {
    if (level > log_level_)
      return;

    std::stringstream ss;
#ifdef _WIN32
    char realtime[50] = {0};
    SYSTEMTIME sys;
    GetLocalTime(&sys);
    // 09-14 17:25:34.825
    snprintf(realtime, sizeof(realtime), "[%02d-%02d %02d:%02d:%02d.%03d] ",
             sys.wMonth, sys.wDay, sys.wHour, sys.wMinute, sys.wSecond,
             sys.wMilliseconds);
    std::string out = "LLog " + std::string(realtime) + " [";
    ss << out << std::this_thread::get_id() << "] ";
#endif
    va_list args;
    va_start(args, format);
    int size = vsnprintf(nullptr, 0, format, args) + 1;
    va_end(args);

    char* buffer = new char[size];

    va_start(args, format);
    vsnprintf(buffer, size, format, args);
    va_end(args);

    ss << buffer;
    delete[] buffer;

#ifdef _WIN32
    if (file_.is_open()) {
      mutex_.lock();
      file_ << ss.str() << std::endl;
      mutex_.unlock();
    } else {
      mutex_.lock();
      std::cout << ss.str() << std::endl;
      mutex_.unlock();
    }
#else
    __android_log_print(ANDROID_LOG_INFO, "LLog", "%s", ss.str().c_str());
#endif
    return;
  }
  static void Log(log_level level, std::string msg) {
    if (level > log_level_)
      return;

    std::stringstream ss;
#ifdef _WIN32
    char realtime[50] = {0};
    SYSTEMTIME sys;
    GetLocalTime(&sys);
    // 09-14 17:25:34.825
    snprintf(realtime, sizeof(realtime), "[%02d-%02d %02d:%02d:%02d.%03d] ",
             sys.wMonth, sys.wDay, sys.wHour, sys.wMinute, sys.wSecond,
             sys.wMilliseconds);
    std::string out = "LLog " + std::string(realtime) + " [";
    ss << out << std::this_thread::get_id() << "] ";
#endif
    ss << msg;
#ifdef _WIN32
    if (file_.is_open()) {
      mutex_.lock();
      file_ << ss.str() << std::endl;
      mutex_.unlock();
    } else {
      mutex_.lock();
      std::cout << ss.str() << std::endl;
      mutex_.unlock();
    }
#else
    __android_log_print(ANDROID_LOG_INFO, "LLog", "%s", ss.str().c_str());
#endif
  }

 private:
  static log_level log_level_;  // TODO set dynamic
  static bool isinit_;
#ifdef _WIN32
  static RotatingLogFile file_;  //("D:\\p2ptest.log", std::ios::app);
#endif
  static std::mutex mutex_;
};

#endif
