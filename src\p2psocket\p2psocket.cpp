// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include "p2psocket.h"

#include <memory>
#include <unordered_map>

#include "lenlog.h"
#include "p2psocketfactory.h"

static P2pSocketFactory* factory = nullptr;
static std::mutex mutex_;
static int socket_count = 0;
static bool issslinit = false;
std::unordered_map<P2P_SOCKET, std::shared_ptr<SocketInterface>> g_set_;

P2P_SOCKET P2pCreate2(SocketOptions* option, bool isaccept) {
  if (option == nullptr)
    return nullptr;
  if (option->cert == nullptr || option->privatekey == nullptr) {
    LLog::Log(LOG_ERROR, "cert or key is null");
    return nullptr;
  }
  if (option->log_level > P2P_LOG_NONE &&
      option->log_level <= P2P_LOG_VERBOSE) {
    LLog::Init(option->log_level, option->log_path);
  }

  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (factory == nullptr) {
      factory = new P2pSocketFactory();
    }
    if (option != nullptr && !issslinit) {
      factory->SSLInit();
      issslinit = true;
    }
  }
  int socket_type_;
  LLog::Log(LOG_INFO, "worknum:%d, type:%d, mode %d", option->wokernum,
            (int)option->type, (int)option->mode);
  switch (option->type) {
    case SOCKET_MULTITCP:
      socket_type_ = P2pSocketFactory::MUTILTCP;
      break;
    case SOCKET_SSL:
      socket_type_ = P2pSocketFactory::SSL;
      break;
    case SOCKET_QUIC:
        socket_type_ = P2pSocketFactory::QUIC;
      break;
    default:
      socket_type_ = P2pSocketFactory::MUTILTCP;
  }
  std::shared_ptr<SocketInterface> socket_ptr(static_cast<SocketInterface*>(
      factory->CreateP2pSocket(socket_type_, option, isaccept, true)));

  if (!socket_ptr) {
    return nullptr;
  }

  P2P_SOCKET s = socket_ptr.get();
  CertVerifyCallback cb = option->cert_verify;
  LLog::Log(LOG_INFO, "CertVerifyCallback cb: %p", cb);
  if (cb != nullptr) {
    auto cb_lambda = ([=](std::string& str) -> bool {
      LLog::Log(LOG_INFO, "verify callback event ");
      return cb(s, str.c_str());
    });
    socket_ptr->P2pSetCertVerifyCallback(cb_lambda);
  }else{
    LLog::Log(LOG_INFO, "CertVerifyCallback cb == nullptr");
    return nullptr;
  }
  {
    std::lock_guard<std::mutex> lock(mutex_);
    socket_count++;
    g_set_[s] = socket_ptr;
  }
  return s;
}
void AddtoSocketSet(SocketInterface* soc) {
  if (soc == nullptr)
    return;
  std::shared_ptr<SocketInterface> socket_ptr(soc);
  std::lock_guard<std::mutex> lock(mutex_);
  socket_count++;
  g_set_[soc] = socket_ptr;
}
static std::shared_ptr<SocketInterface> GetSocketSafe(P2P_SOCKET soc) {
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = g_set_.find(soc);
  return (it != g_set_.end()) ? it->second : nullptr;
}
P2P_SOCKET P2pCreate(SocketOptions* option) {
  return P2pCreate2(option, false);
}

int P2pSetNonBlocking(P2P_SOCKET soc) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pSetNonBlocking() : -1;
}

int P2pSetConnTimeout(P2P_SOCKET soc, int timeout) {
  if (timeout < 0)
    return -1;
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pSetConnTimeout(timeout) : -1;
}

int P2pPoll(P2P_SOCKET soc, PollEvent* event, int timeout) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pPoll(event, timeout) : -1;
}

int P2pBind(P2P_SOCKET soc, const char* ipaddr, int port) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pBind(ipaddr, port) : -1;
}

int P2pBind2(P2P_SOCKET soc, const char* ipaddr, int port[], int size) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pBind2(ipaddr, port, size) : -1;
}

int P2pRegisterStateChanged(P2P_SOCKET soc, P2pStateChanged callback) {
  auto socket_ptr = GetSocketSafe(soc);
  if (!socket_ptr)
    return -1;

  auto cb_lambda = [=](int event, int parm) {
    LLog::Log(LOG_INFO, "callback event:%d, parm:%d", event, parm);
    callback(soc, event, parm);
  };
  return socket_ptr->P2pRegisterStateChanged(cb_lambda);
}
int P2pConnect(P2P_SOCKET soc, const char* ipaddr, int port) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pConnect(ipaddr, port) : -1;
}

P2P_SOCKET P2pAccept(P2P_SOCKET soc, char* ipaddr, int ipaddr_len, int* port) {
  auto socket_ptr = GetSocketSafe(soc);
  if (!socket_ptr)
    return nullptr;

  SocketInterface* cli = socket_ptr->P2pAccept(ipaddr, ipaddr_len, port);
  if (cli)
    AddtoSocketSet(cli);
  return cli;
}
int P2pListen(P2P_SOCKET soc) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pListen() : -1;
}

int P2pWrite(P2P_SOCKET soc, const char* buffer, int len) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pWrite(buffer, len) : -1;
}

int P2pRead(P2P_SOCKET soc, char* buffer, int len) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pRead(buffer, len) : -1;
}

int P2pWritev(P2P_SOCKET soc, struct p2p_iovec* iov, int count) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pWritev(iov, count) : -1;
}

int P2pGetLocalPort(P2P_SOCKET soc) {
  auto socket_ptr = GetSocketSafe(soc);
  return socket_ptr ? socket_ptr->P2pGetLocalPort() : -1;
}

int P2pClose(P2P_SOCKET soc) {
  std::shared_ptr<SocketInterface> socket_ptr;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = g_set_.find(soc);
    if (it == g_set_.end())
      return -1;

    socket_ptr = it->second;
    g_set_.erase(it);
    socket_count--;

    LLog::Log(LOG_INFO, "P2pClose socket_count %d", socket_count);

    if (socket_count == 0 && factory != nullptr) {
      if (issslinit == true) {
        factory->SSLUninit();
        issslinit = false;
      }
      delete factory;
      factory = nullptr;
    }
  }

  int ret= socket_ptr->P2pClose();
  socket_ptr = nullptr;
  if(socket_count == 0)
      LLog::Uninit();
  return ret;
}

int P2pSetSendMode(P2P_SOCKET soc, int directMode) {
    auto socket_ptr = GetSocketSafe(soc);
    return socket_ptr ? socket_ptr->P2pSetSendMode(directMode) : -1;
}
int P2pSetReadMode(P2P_SOCKET soc, int directMode) {
    auto socket_ptr = GetSocketSafe(soc);
    return socket_ptr ? socket_ptr->P2pSetReadMode(directMode) : -1;
}

// Stream API implementations - dispatch to the underlying socket implementation
P2P_STREAM P2pStreamCreate(P2P_SOCKET soc, struct StreamOptions* options) {
    auto socket_ptr = GetSocketSafe(soc);
    return socket_ptr ? socket_ptr->P2pStreamCreate(options) : nullptr;
}

int P2pStreamClose(P2P_STREAM stream) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamClose(stream) : -1;
}

int P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamWrite(stream, buffer, len) : -1;
}

int P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamWritev(stream, iov, count) : -1;
}

int P2pStreamRead(P2P_STREAM stream, char* buffer, int len) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamRead(stream, buffer, len) : -1;
}

int P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamPoll(stream, events, max_events, timeout) : -1;
}

int P2pStreamGetState(P2P_STREAM stream) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamGetState(stream) : -1;
}

int P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamSetCallback(stream, callback, user_data) : -1;
}

int P2pStreamGetId(P2P_STREAM stream) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamGetId(stream) : -1;
}

int P2pStreamGetBufferedBytes(P2P_STREAM stream) {
    if (stream == nullptr) {
        return -1;
    }

    // Get the parent socket from the stream
    P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
    if (parent_socket == nullptr) {
        return -1;
    }

    auto socket_ptr = GetSocketSafe(parent_socket);
    return socket_ptr ? socket_ptr->P2pStreamGetBufferedBytes(stream) : -1;
}

P2P_SOCKET P2pStreamGetSocket(P2P_STREAM stream) {
    if (stream == nullptr) {
        return nullptr;
    }

    // This function needs to be implemented differently since we can't call itself
    // We need to access the stream context directly
    // For now, we'll implement this in the QUIC-specific code

    // Try to find the socket by checking all sockets in g_set_
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& pair : g_set_) {
        auto socket_ptr = pair.second;
        if (socket_ptr) {
            P2P_SOCKET result = socket_ptr->P2pStreamGetSocket(stream);
            if (result != nullptr) {
                return result;
            }
        }
    }

    return nullptr;
}
